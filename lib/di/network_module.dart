import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/data/network/api_client.dart';
import 'package:sales_app/data/network/interceptors/auth_interceptor.dart';
import 'package:sales_app/data/network/interceptors/logging_interceptor.dart';
import 'package:sales_app/data/network/interceptors/error_interceptor.dart';
import 'package:sales_app/data/network/interceptors/mock/mock_auth_interceptor.dart';
import 'package:sales_app/domain/services/environment_service.dart';

@module
abstract class NetworkModule {
  
  /// Default API Client with all interceptors
  /// Used for most API services that need authentication
  @Named('default')
  @singleton
  ApiClient defaultApiClient(
    AuthInterceptor authInterceptor,
    LoggingInterceptor loggingInterceptor,
    ErrorInterceptor errorInterceptor,
    MockAuthInterceptor mockAuthInterceptor,
    EnvironmentService environmentService,
  ) {
    return ApiClient(
      interceptors: [
        loggingInterceptor,
        mockAuthInterceptor, // Mock should come before auth
        authInterceptor,
        errorInterceptor,
      ],
      config: DioConfig.createDefaultConfig(
        environmentService.baseUrl,
        environmentService.timeoutDuration,
      ),
    );
  }

  /// Auth API Client without AuthInterceptor
  /// Used for authentication endpoints (login, register, etc.)
  @Named('auth')
  @singleton
  ApiClient authApiClient(
    LoggingInterceptor loggingInterceptor,
    ErrorInterceptor errorInterceptor,
    MockAuthInterceptor mockAuthInterceptor,
    EnvironmentService environmentService,
  ) {
    return ApiClient(
      interceptors: [
        loggingInterceptor,
        mockAuthInterceptor, // Keep mock for development
        errorInterceptor,
      ],
      config: DioConfig.createAuthConfig(environmentService.baseUrl),
    );
  }

  /// Public API Client with minimal interceptors
  /// Used for public endpoints that don't need authentication or mocking
  @Named('public')
  @singleton
  ApiClient publicApiClient(
    LoggingInterceptor loggingInterceptor,
    ErrorInterceptor errorInterceptor,
    EnvironmentService environmentService,
  ) {
    return ApiClient(
      interceptors: [
        loggingInterceptor,
        errorInterceptor,
      ],
      config: DioConfig.createDefaultConfig(
        environmentService.baseUrl,
        environmentService.timeoutDuration,
      ),
    );
  }

  /// Upload API Client with extended timeouts
  /// Used for file upload operations
  @Named('upload')
  @singleton
  ApiClient uploadApiClient(
    AuthInterceptor authInterceptor,
    LoggingInterceptor loggingInterceptor,
    ErrorInterceptor errorInterceptor,
    MockAuthInterceptor mockAuthInterceptor,
    EnvironmentService environmentService,
  ) {
    return ApiClient(
      interceptors: [
        loggingInterceptor,
        mockAuthInterceptor,
        authInterceptor,
        errorInterceptor,
      ],
      config: DioConfig.createUploadConfig(environmentService.baseUrl),
    );
  }

  /// Legacy Dio instance for backward compatibility
  /// This can be removed once all services use ApiClient
  @singleton
  Dio legacyDio(
    AuthInterceptor authInterceptor,
    LoggingInterceptor loggingInterceptor,
    ErrorInterceptor errorInterceptor,
    MockAuthInterceptor mockAuthInterceptor,
    EnvironmentService environmentService,
  ) {
    final dio = Dio(
      BaseOptions(
        baseUrl: environmentService.baseUrl,
        connectTimeout: Duration(seconds: environmentService.timeoutDuration ~/ 1000),
        receiveTimeout: Duration(seconds: environmentService.timeoutDuration ~/ 1000),
        sendTimeout: Duration(seconds: environmentService.timeoutDuration ~/ 1000),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    // Add interceptors in the correct order
    dio.interceptors.addAll([
      loggingInterceptor,
      mockAuthInterceptor,
      authInterceptor,
      errorInterceptor,
    ]);

    return dio;
  }
}
