// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// **************************************************************************
// FloorGenerator
// **************************************************************************

abstract class $AppDatabaseBuilderContract {
  /// Adds migrations to the builder.
  $AppDatabaseBuilderContract addMigrations(List<Migration> migrations);

  /// Adds a database [Callback] to the builder.
  $AppDatabaseBuilderContract addCallback(Callback callback);

  /// Creates the database and initializes it.
  Future<AppDatabase> build();
}

// ignore: avoid_classes_with_only_static_members
class $FloorAppDatabase {
  /// Creates a database builder for a persistent database.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDatabaseBuilderContract databaseBuilder(String name) =>
      _$AppDatabaseBuilder(name);

  /// Creates a database builder for an in memory database.
  /// Information stored in an in memory database disappears when the process is killed.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDatabaseBuilderContract inMemoryDatabaseBuilder() =>
      _$AppDatabaseBuilder(null);
}

class _$AppDatabaseBuilder implements $AppDatabaseBuilderContract {
  _$AppDatabaseBuilder(this.name);

  final String? name;

  final List<Migration> _migrations = [];

  Callback? _callback;

  @override
  $AppDatabaseBuilderContract addMigrations(List<Migration> migrations) {
    _migrations.addAll(migrations);
    return this;
  }

  @override
  $AppDatabaseBuilderContract addCallback(Callback callback) {
    _callback = callback;
    return this;
  }

  @override
  Future<AppDatabase> build() async {
    final path = name != null
        ? await sqfliteDatabaseFactory.getDatabasePath(name!)
        : ':memory:';
    final database = _$AppDatabase();
    database.database = await database.open(
      path,
      _migrations,
      _callback,
    );
    return database;
  }
}

class _$AppDatabase extends AppDatabase {
  _$AppDatabase([StreamController<String>? listener]) {
    changeListener = listener ?? StreamController<String>.broadcast();
  }

  UserDao? _userDaoInstance;

  Future<sqflite.Database> open(
    String path,
    List<Migration> migrations, [
    Callback? callback,
  ]) async {
    final databaseOptions = sqflite.OpenDatabaseOptions(
      version: 1,
      onConfigure: (database) async {
        await database.execute('PRAGMA foreign_keys = ON');
        await callback?.onConfigure?.call(database);
      },
      onOpen: (database) async {
        await callback?.onOpen?.call(database);
      },
      onUpgrade: (database, startVersion, endVersion) async {
        await MigrationAdapter.runMigrations(
            database, startVersion, endVersion, migrations);

        await callback?.onUpgrade?.call(database, startVersion, endVersion);
      },
      onCreate: (database, version) async {
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `UserLocalModel` (`id` TEXT NOT NULL, `email` TEXT NOT NULL, `name` TEXT NOT NULL, `avatar` TEXT, `phone` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `syncStatus` TEXT NOT NULL, `serverUpdatedAt` INTEGER, PRIMARY KEY (`id`))');

        await callback?.onCreate?.call(database, version);
      },
    );
    return sqfliteDatabaseFactory.openDatabase(path, options: databaseOptions);
  }

  @override
  UserDao get userDao {
    return _userDaoInstance ??= _$UserDao(database, changeListener);
  }
}

class _$UserDao extends UserDao {
  _$UserDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database),
        _userLocalModelInsertionAdapter = InsertionAdapter(
            database,
            'UserLocalModel',
            (UserLocalModel item) => <String, Object?>{
                  'id': item.id,
                  'email': item.email,
                  'name': item.name,
                  'avatar': item.avatar,
                  'phone': item.phone,
                  'createdAt': item.createdAt,
                  'updatedAt': item.updatedAt,
                  'syncStatus': item.syncStatus,
                  'serverUpdatedAt': item.serverUpdatedAt
                }),
        _userLocalModelUpdateAdapter = UpdateAdapter(
            database,
            'UserLocalModel',
            ['id'],
            (UserLocalModel item) => <String, Object?>{
                  'id': item.id,
                  'email': item.email,
                  'name': item.name,
                  'avatar': item.avatar,
                  'phone': item.phone,
                  'createdAt': item.createdAt,
                  'updatedAt': item.updatedAt,
                  'syncStatus': item.syncStatus,
                  'serverUpdatedAt': item.serverUpdatedAt
                });

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<UserLocalModel> _userLocalModelInsertionAdapter;

  final UpdateAdapter<UserLocalModel> _userLocalModelUpdateAdapter;

  @override
  Future<UserLocalModel?> getUserById(String id) async {
    return _queryAdapter.query('SELECT * FROM users WHERE id = ?1',
        mapper: (Map<String, Object?> row) => UserLocalModel(
            id: row['id'] as String,
            email: row['email'] as String,
            name: row['name'] as String,
            avatar: row['avatar'] as String?,
            phone: row['phone'] as String?,
            createdAt: row['createdAt'] as int,
            updatedAt: row['updatedAt'] as int,
            syncStatus: row['syncStatus'] as String,
            serverUpdatedAt: row['serverUpdatedAt'] as int?),
        arguments: [id]);
  }

  @override
  Future<UserLocalModel?> getUserByEmail(String email) async {
    return _queryAdapter.query('SELECT * FROM users WHERE email = ?1',
        mapper: (Map<String, Object?> row) => UserLocalModel(
            id: row['id'] as String,
            email: row['email'] as String,
            name: row['name'] as String,
            avatar: row['avatar'] as String?,
            phone: row['phone'] as String?,
            createdAt: row['createdAt'] as int,
            updatedAt: row['updatedAt'] as int,
            syncStatus: row['syncStatus'] as String,
            serverUpdatedAt: row['serverUpdatedAt'] as int?),
        arguments: [email]);
  }

  @override
  Future<List<UserLocalModel>> getAllUsers() async {
    return _queryAdapter.queryList(
        'SELECT * FROM users ORDER BY updatedAt DESC',
        mapper: (Map<String, Object?> row) => UserLocalModel(
            id: row['id'] as String,
            email: row['email'] as String,
            name: row['name'] as String,
            avatar: row['avatar'] as String?,
            phone: row['phone'] as String?,
            createdAt: row['createdAt'] as int,
            updatedAt: row['updatedAt'] as int,
            syncStatus: row['syncStatus'] as String,
            serverUpdatedAt: row['serverUpdatedAt'] as int?));
  }

  @override
  Future<void> deleteUserById(String id) async {
    await _queryAdapter
        .queryNoReturn('DELETE FROM users WHERE id = ?1', arguments: [id]);
  }

  @override
  Future<void> deleteAllUsers() async {
    await _queryAdapter.queryNoReturn('DELETE FROM users');
  }

  @override
  Future<List<UserLocalModel>> getUsersBySyncStatus(String status) async {
    return _queryAdapter.queryList('SELECT * FROM users WHERE syncStatus = ?1',
        mapper: (Map<String, Object?> row) => UserLocalModel(
            id: row['id'] as String,
            email: row['email'] as String,
            name: row['name'] as String,
            avatar: row['avatar'] as String?,
            phone: row['phone'] as String?,
            createdAt: row['createdAt'] as int,
            updatedAt: row['updatedAt'] as int,
            syncStatus: row['syncStatus'] as String,
            serverUpdatedAt: row['serverUpdatedAt'] as int?),
        arguments: [status]);
  }

  @override
  Future<List<UserLocalModel>> getPendingSyncUsers() async {
    return _queryAdapter.queryList(
        'SELECT * FROM users WHERE syncStatus = \"pending_sync\"',
        mapper: (Map<String, Object?> row) => UserLocalModel(
            id: row['id'] as String,
            email: row['email'] as String,
            name: row['name'] as String,
            avatar: row['avatar'] as String?,
            phone: row['phone'] as String?,
            createdAt: row['createdAt'] as int,
            updatedAt: row['updatedAt'] as int,
            syncStatus: row['syncStatus'] as String,
            serverUpdatedAt: row['serverUpdatedAt'] as int?));
  }

  @override
  Future<List<UserLocalModel>> getFailedSyncUsers() async {
    return _queryAdapter.queryList(
        'SELECT * FROM users WHERE syncStatus = \"sync_failed\"',
        mapper: (Map<String, Object?> row) => UserLocalModel(
            id: row['id'] as String,
            email: row['email'] as String,
            name: row['name'] as String,
            avatar: row['avatar'] as String?,
            phone: row['phone'] as String?,
            createdAt: row['createdAt'] as int,
            updatedAt: row['updatedAt'] as int,
            syncStatus: row['syncStatus'] as String,
            serverUpdatedAt: row['serverUpdatedAt'] as int?));
  }

  @override
  Future<void> updateSyncStatus(
    String id,
    String status,
    int timestamp,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE users SET syncStatus = ?2, updatedAt = ?3 WHERE id = ?1',
        arguments: [id, status, timestamp]);
  }

  @override
  Future<void> markUserAsSynced(
    String id,
    int serverTimestamp,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE users SET syncStatus = \"synced\", serverUpdatedAt = ?2 WHERE id = ?1',
        arguments: [id, serverTimestamp]);
  }

  @override
  Future<int?> getUserCount(String id) async {
    return _queryAdapter.query('SELECT COUNT(*) FROM users WHERE id = ?1',
        mapper: (Map<String, Object?> row) => row.values.first as int,
        arguments: [id]);
  }

  @override
  Future<int?> getTotalUsersCount() async {
    return _queryAdapter.query('SELECT COUNT(*) FROM users',
        mapper: (Map<String, Object?> row) => row.values.first as int);
  }

  @override
  Future<List<UserLocalModel>> getUsersCreatedAfter(int timestamp) async {
    return _queryAdapter.queryList(
        'SELECT * FROM users WHERE createdAt > ?1 ORDER BY createdAt DESC',
        mapper: (Map<String, Object?> row) => UserLocalModel(
            id: row['id'] as String,
            email: row['email'] as String,
            name: row['name'] as String,
            avatar: row['avatar'] as String?,
            phone: row['phone'] as String?,
            createdAt: row['createdAt'] as int,
            updatedAt: row['updatedAt'] as int,
            syncStatus: row['syncStatus'] as String,
            serverUpdatedAt: row['serverUpdatedAt'] as int?),
        arguments: [timestamp]);
  }

  @override
  Future<List<UserLocalModel>> getUsersUpdatedAfter(int timestamp) async {
    return _queryAdapter.queryList(
        'SELECT * FROM users WHERE updatedAt > ?1 ORDER BY updatedAt DESC',
        mapper: (Map<String, Object?> row) => UserLocalModel(
            id: row['id'] as String,
            email: row['email'] as String,
            name: row['name'] as String,
            avatar: row['avatar'] as String?,
            phone: row['phone'] as String?,
            createdAt: row['createdAt'] as int,
            updatedAt: row['updatedAt'] as int,
            syncStatus: row['syncStatus'] as String,
            serverUpdatedAt: row['serverUpdatedAt'] as int?),
        arguments: [timestamp]);
  }

  @override
  Future<void> insertUser(UserLocalModel user) async {
    await _userLocalModelInsertionAdapter.insert(
        user, OnConflictStrategy.abort);
  }

  @override
  Future<void> updateUser(UserLocalModel user) async {
    await _userLocalModelUpdateAdapter.update(user, OnConflictStrategy.abort);
  }
}
