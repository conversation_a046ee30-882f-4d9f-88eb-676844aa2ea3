import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/error/failures.dart';
import 'package:sales_app/core/constants/api_paths.dart';
import 'package:sales_app/data/network/base_api_service.dart';
import 'package:sales_app/data/models/user_model.dart';

/// Request/Response models for auth APIs
class LoginRequest {
  final String email;
  final String password;

  LoginRequest({required this.email, required this.password});

  Map<String, dynamic> toJson() => {
    'email': email,
    'password': password,
  };
}

class LoginResponse {
  final String accessToken;
  final String refreshToken;
  final UserModel user;

  LoginResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.user,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
    accessToken: json['access_token'] ?? '',
    refreshToken: json['refresh_token'] ?? '',
    user: UserModel.fromJson(json['user'] ?? {}),
  );
}



/// Manual Auth API Service
/// This will be replaced/wrapped by generated APIs in the future
@injectable
class AuthApiService extends BaseApiService {
  AuthApiService(@Named('auth') super.apiClient);

  /// Login user
  Future<Either<Failure, LoginResponse>> login(LoginRequest request) async {
    return handleApiCall(
      () => dio.post(
        ApiPaths.authLogin,
        data: createRequestData(request.toJson()),
      ),
      (data) => LoginResponse.fromJson(data),
    );
  }

  /// Logout user
  Future<Either<Failure, void>> logout() async {
    return handleApiCallVoid(
      () => dio.post(ApiPaths.authLogout),
    );
  }
}
