import 'package:floor/floor.dart';
import '../../../domain/entities/user.dart';

/// Local Model cho User trong SQLite database
/// Đây là pure data model, không chứa business logic
@entity
@Entity(tableName: 'users')
class UserLocalModel {
  @primaryKey
  final String id;
  
  final String email;
  final String name;
  final String? avatar;
  final String? phone;
  
  /// Metadata cho local storage
  final int createdAt;
  final int updatedAt;
  
  /// Sync tracking
  final String syncStatus;
  final int? serverUpdatedAt;

  const UserLocalModel({
    required this.id,
    required this.email,
    required this.name,
    this.avatar,
    this.phone,
    required this.createdAt,
    required this.updatedAt,
    this.syncStatus = 'synced',
    this.serverUpdatedAt,
  });

  /// Convert từ Domain Entity sang Local Model
  factory UserLocalModel.fromDomain(User user) {
    final now = DateTime.now().millisecondsSinceEpoch;
    return UserLocalModel(
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      phone: user.phone,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'synced',
    );
  }

  /// Convert từ Local Model sang Domain Entity
  User toDomain() {
    return User(
      id: id,
      email: email,
      name: name,
      avatar: avatar,
      phone: phone,
    );
  }

  /// Create copy với updated fields
  UserLocalModel copyWith({
    String? id,
    String? email,
    String? name,
    String? avatar,
    String? phone,
    int? createdAt,
    int? updatedAt,
    String? syncStatus,
    int? serverUpdatedAt,
  }) {
    return UserLocalModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
      serverUpdatedAt: serverUpdatedAt ?? this.serverUpdatedAt,
    );
  }

  @override
  String toString() {
    return 'UserLocalModel(id: $id, email: $email, name: $name, syncStatus: $syncStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserLocalModel &&
        other.id == id &&
        other.email == email &&
        other.name == name &&
        other.avatar == avatar &&
        other.phone == phone &&
        other.syncStatus == syncStatus;
  }

  @override
  int get hashCode {
    return Object.hash(id, email, name, avatar, phone, syncStatus);
  }
}
