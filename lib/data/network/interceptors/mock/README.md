# Mock API System - Modular Interceptors

## Overview

This directory contains **modular mock interceptors** that provide mock API functionality organized by domain. Each interceptor handles specific API endpoints, making the system scalable and maintainable.

**Current Status**: ✅ Ready to use with Auth APIs mock implementation.

## 🚀 Quick Start

### **1. Test Login**
```dart
// App is already in Mock Mode (AppConstants.isMockMode = true)
await authController.login(
  email: '<EMAIL>',
  password: '123456',
);
```

### **2. Available Mock Users**
| Email | Password | Name | Role |
|-------|----------|------|------|
| <EMAIL> | 123456 | Admin User | admin |
| <EMAIL> | password | Test User | user |
| <EMAIL> | demo123 | Demo User | demo |

### **3. Switch to Real API**
```dart
// In lib/core/constants/app_constants.dart
static const bool isMockMode = false;
```

## Architecture

### **Base Class**
- `base_mock_interceptor.dart` - Abstract base class with common functionality
- Provides network simulation, response creation, error handling

### **Domain-Specific Interceptors**
- `mock_auth_interceptor.dart` - Authentication APIs (uses `ApiPaths` constants)

### **API Path Constants**
- `lib/core/constants/api_paths.dart` - Centralized API endpoint definitions
- Prevents hardcoding paths in interceptors and services

## How It Works

### **Request Flow:**
1. **Real API Mode**: Request passes through all interceptors → Real API
2. **Mock Mode**: Appropriate interceptor catches request → Returns mock data

### **Interceptor Order:**
```
LoggingInterceptor
↓
MockAuthInterceptor     ← Catches /auth/* requests
↓
AuthInterceptor         ← Adds auth headers (if request not caught)
↓
ErrorInterceptor        ← Handles errors
↓
Real API Call (if not mocked)
```

## Features

### **Mock Auth Interceptor**
- ✅ **Login**: `ApiPaths.authLogin` - Validates credentials against mock users
- ✅ **Logout**: `ApiPaths.authLogout` - Always succeeds
- ✅ **Register**: `ApiPaths.authRegister` - Mock registration
- ✅ **Refresh Token**: `ApiPaths.authRefresh` - Mock token refresh

**Mock Users:**
| Email | Password | Name | Role |
|-------|----------|------|------|
| <EMAIL> | 123456 | Admin User | admin |
| <EMAIL> | password | Test User | user |
| <EMAIL> | demo123 | Demo User | demo |



## Configuration

### **Enable/Disable Mock Mode**
```dart
// Enable mock mode
AppConstants.isMockMode = true;

// Disable mock mode (use real API)
AppConstants.isMockMode = false;
```

### **Network Simulation**
Each interceptor simulates:
- **Realistic delays**: 500ms - 2s random delay
- **Error simulation**: 10% chance of network error (optional)
- **Proper HTTP status codes**: 200, 401, 404, 500, etc.
- **API Paths**: Uses `ApiPaths` constants instead of hardcoding

## Adding New Mock Interceptors (Future)

When you need to add more mock APIs (e.g., for users, products, orders), you can follow this pattern:

### **Step 1: Create New Interceptor**
```dart
// lib/data/network/interceptors/mock/mock_user_interceptor.dart
@injectable
class MockUserInterceptor extends BaseMockInterceptor {
  @override
  bool shouldHandle(RequestOptions options) {
    return ApiPaths.isUserPath(options.path);
  }

  @override
  void handleMockRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    await simulateNetworkDelay();

    if (options.path.contains(ApiPaths.userProfile) && options.method == 'GET') {
      _handleGetProfile(options, handler);
    }
    // ... other handlers
  }

  void _handleGetProfile(RequestOptions options, RequestInterceptorHandler handler) {
    final profileData = {
      'id': '1',
      'email': '<EMAIL>',
      'name': 'Current User',
    };
    handler.resolve(createSuccessResponse(options, profileData));
  }
}
```

### **Step 2: Register in ApiClient**
```dart
// lib/data/network/api_client.dart
class ApiClient {
  final MockUserInterceptor _mockUserInterceptor;

  ApiClient(
    // ... existing interceptors
    this._mockUserInterceptor,
  );

  List<Interceptor> _getDefaultInterceptors() {
    return [
      _loggingInterceptor,
      _mockAuthInterceptor,
      _mockUserInterceptor,  // Add here
      _authInterceptor,
      _errorInterceptor,
    ];
  }
}
```

### **Step 3: Regenerate DI**
```bash
dart run build_runner build --delete-conflicting-outputs
```

## Benefits

### **✅ Advantages**
- **Scalable**: Easy to add new domain interceptors
- **Maintainable**: Each domain has its own file
- **Team-friendly**: Multiple developers can work on different interceptors
- **Clean**: No mixing of mock/real logic in API services
- **Flexible**: Can mock some APIs while using real ones for others

### **⚠️ Considerations**
- More files to manage
- Need to understand interceptor order
- Debugging requires checking multiple interceptors

## Testing

### **Manual Testing**
1. Set mock mode: `AppConstants.isMockMode = true`
2. Use app normally
3. Check console for "Mock Interceptor" logs
4. Verify mock data is returned

### **Unit Testing**
```dart
test('Mock auth interceptor handles login', () async {
  final interceptor = MockAuthInterceptor();
  final options = RequestOptions(path: '/auth/login', method: 'POST');
  options.data = {'email': '<EMAIL>', 'password': '123456'};
  
  // Test interceptor logic
  expect(interceptor.shouldHandle(options), true);
});
```

## Migration Path

When real APIs become available:

1. **Keep interceptors**: Useful for testing and development
2. **Switch mode**: Change to `AppConstants.isMockMode = false`
3. **Gradual migration**: Can mock some APIs while using real ones for others
4. **Remove when ready**: Delete interceptors when no longer needed

## Best Practices

1. **Keep mock data realistic** - Use proper data structures
2. **Simulate errors** - Test error handling paths
3. **Log everything** - Use AppLogger for debugging
4. **Follow REST conventions** - Proper HTTP status codes
5. **Document endpoints** - Update this README when adding new mocks
