import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/error/failures.dart';
import 'package:sales_app/core/error/error_types.dart';

@injectable
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Transform DioException to custom failure types
    final failure = _mapDioExceptionToFailure(err);

    // Create a new DioException with custom failure information
    final customError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: failure,
      message: 'Error occurred', // Generic message, actual message will be handled in presentation layer
    );

    handler.next(customError);
  }

  Failure _mapDioExceptionToFailure(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return const NetworkFailure(NetworkErrorType.connectionTimeout);

      case DioExceptionType.sendTimeout:
        return const NetworkFailure(NetworkErrorType.sendTimeout);

      case DioExceptionType.receiveTimeout:
        return const NetworkFailure(NetworkErrorType.receiveTimeout);

      case DioExceptionType.badResponse:
        return _handleBadResponse(error);

      case DioExceptionType.cancel:
        return const NetworkFailure(NetworkErrorType.requestCancelled);

      case DioExceptionType.connectionError:
        return const NetworkFailure(NetworkErrorType.noConnection);

      case DioExceptionType.badCertificate:
        return const NetworkFailure(NetworkErrorType.certificateError);

      case DioExceptionType.unknown:
        return const ServerFailure(ServerErrorType.unknown);
    }
  }

  Failure _handleBadResponse(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    switch (statusCode) {
      case 400:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ValidationFailure(
          ValidationErrorType.serverValidation,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 401:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return AuthFailure(
          AuthErrorType.invalidCredentials,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 403:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return AuthFailure(
          AuthErrorType.accessDenied,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 404:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ServerFailure(
          ServerErrorType.notFound,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 422:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ValidationFailure(
          ValidationErrorType.serverValidation,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 429:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ServerFailure(
          ServerErrorType.tooManyRequests,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ServerFailure(
          ServerErrorType.internalError,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      default:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ServerFailure(
          ServerErrorType.unknown,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );
    }
  }

  String? _extractErrorMessage(dynamic responseData) {
    if (responseData == null) return null;

    try {
      if (responseData is Map<String, dynamic>) {
        // Try common error message fields
        return responseData['message'] ??
               responseData['error'] ??
               responseData['detail'] ??
               responseData['errors']?.toString();
      }

      if (responseData is String) {
        return responseData;
      }
    } catch (e) {
      // If parsing fails, return null
    }

    return null;
  }

  String? _extractErrorCode(dynamic responseData) {
    if (responseData == null) return null;

    try {
      if (responseData is Map<String, dynamic>) {
        // Try common error code fields
        return responseData['error_code'] ??
               responseData['errorCode'] ??
               responseData['code'] ??
               responseData['status_code']?.toString();
      }
    } catch (e) {
      // If parsing fails, return null
    }

    return null;
  }
}
