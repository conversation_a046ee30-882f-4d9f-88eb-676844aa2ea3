import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/domain/services/storage_service.dart';

@injectable
class AuthInterceptor extends Interceptor {
  final StorageService _storageService;

  AuthInterceptor(this._storageService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      final token = await _storageService.getAccessToken();
      if (token != null && token.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      // Log error but continue with request
      AppLogger.error('Error adding auth token', error: e);
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Handle 401 Unauthorized
    if (err.response?.statusCode == 401) {
      await _handleUnauthorized();
    }

    handler.next(err);
  }



  Future<void> _handleUnauthorized() async {
    try {
      // Clear stored tokens
      await _storageService.clearTokens();

      // TODO: Navigate to login screen or trigger logout event
      // This could be done through a global event bus or navigation service
    } catch (e) {
      AppLogger.error('Error handling unauthorized', error: e);
    }
  }
}
