import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/constants/storage_keys.dart';
import '../../domain/services/storage_service.dart';
import '../../domain/entities/user.dart';
import '../models/user_model.dart';

@LazySingleton(as: StorageService)
class StorageServiceImpl implements StorageService {
  final SharedPreferences _prefs;
  final FlutterSecureStorage _secureStorage;

  StorageServiceImpl(this._prefs, this._secureStorage);

  // Auth token methods
  @override
  Future<void> saveAccessToken(String token) async {
    await _secureStorage.write(key: StorageKeys.accessToken, value: token);
  }

  @override
  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: StorageKeys.accessToken);
  }

  @override
  Future<void> saveRefreshToken(String token) async {
    await _secureStorage.write(key: StorageKeys.refreshToken, value: token);
  }

  @override
  Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: StorageKeys.refreshToken);
  }

  @override
  Future<void> saveUserInfo(User user) async {
    final userModel = UserModel.fromEntity(user);
    await _prefs.setString(StorageKeys.userInfo, jsonEncode(userModel.toJson()));
  }

  @override
  Future<User?> getUserInfo() async {
    final userStr = _prefs.getString(StorageKeys.userInfo);
    if (userStr == null) return null;

    final userModel = UserModel.fromJson(jsonDecode(userStr));
    return userModel.toEntity();
  }

  // Environment configuration methods
  @override
  Future<void> saveSelectedEnvironment(String environmentKey) async {
    await _prefs.setString(StorageKeys.selectedEnvironment, environmentKey);
  }

  @override
  Future<String?> getSelectedEnvironment() async {
    return _prefs.getString(StorageKeys.selectedEnvironment);
  }



  @override
  Future<void> clearAll() async {
    await Future.wait([
      _secureStorage.deleteAll(),
      _prefs.clear(),
    ]);
  }

  @override
  Future<void> clearTokens() async {
    await Future.wait([
      _secureStorage.delete(key: StorageKeys.accessToken),
      _secureStorage.delete(key: StorageKeys.refreshToken),
    ]);
  }


}