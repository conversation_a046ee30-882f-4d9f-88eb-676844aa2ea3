# 🔧 Systems Guide

## 📋 Table of Contents

1. [Environment Configuration System](#-environment-configuration-system) ✅
2. [Navigation System](#-navigation-system)
3. [Logging System](#-logging-system)
4. [Loading System](#-loading-system)
5. [Mock API System](#-mock-api-system)

## 🌍 Environment Configuration System ✅ **COMPLETED**

**Purpose**: Multi-environment support with dynamic configuration switching
**Status**: Production Ready
**Documentation**: `ENVIRONMENT_CONFIGURATION.md`

### **Key Features:**
- Dev/Staging/Prod environment switching via UI dropdown
- Clean Architecture implementation with domain/data layer separation
- Performance-optimized static map configuration (O(1) lookup)
- Reactive UI updates with StreamBuilder
- Persistent environment selection across app restarts
- Centralized URL and configuration management

### **Architecture:**
```
Domain Layer: EnvironmentService interface + EnvironmentEntity
Data Layer: EnvironmentServiceImpl with static map optimization
Core Layer: EnvironmentConstants for centralized configuration
Presentation Layer: EnvironmentDropdown widget with reactive updates
```

### **Usage:**
```dart
// Get current environment
final environmentService = getIt<EnvironmentService>();
final currentEnv = environmentService.currentEnvironment;

// Switch environment
await environmentService.switchEnvironment(Environment.staging);

// Listen to changes
environmentService.environmentChanges.listen((config) {
  print('Environment: ${config.environment.displayName}');
});
```

### **Files:**
- `lib/domain/entities/environment_entity.dart`
- `lib/domain/services/environment_service.dart`
- `lib/data/services_impl/environment_service_impl.dart`
- `lib/core/constants/environment_constants.dart`
- `lib/presentation/screens/auth/widgets/environment_dropdown.dart`

## 🧭 Navigation System

### 📋 Overview

Sales App sử dụng **Go Router** với **Clean Architecture** để quản lý navigation. Hệ thống được thiết kế để:

- ✅ **Type-safe navigation** với auto-completion
- ✅ **Clean Architecture compliance** - không vi phạm dependency direction
- ✅ **Proper back button behavior** - push vs replace patterns
- ✅ **Centralized route management** - dễ maintain và scale

### 🏗️ Architecture

```
┌─────────────────────────────────────────┐
│           Presentation Layer            │
│  ┌─────────────────────────────────────┐ │
│  │        Router Configuration         │ │
│  │  • app_router.dart                  │ │
│  │  • navigation_extensions.dart       │ │
│  │  • Screen imports & routing logic   │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│              Core Layer                 │
│  ┌─────────────────────────────────────┐ │
│  │         Route Constants             │ │
│  │  • app_routes.dart                  │ │
│  │  • Route validation                 │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 🛣️ Available Routes

| Route | Path | Screen | Use Case |
|-------|------|--------|----------|
| **Auth Routes** |
| `login` | `/login` | LoginScreen | App entry point |
| `selectAccountType` | `/select-account-type` | SelectAccountTypePage | Registration flow |
| `personalInfoConfirmation` | `/personal-info-confirmation` | PersonalInfoConfirmationScreen | Registration flow |
| `ctvPolicy` | `/ctv-policy` | CTVPolicyScreen | Registration flow |
| `kycIdGuide` | `/kyc-id-guide` | KycIdGuideScreen | Registration flow |
| `registrationSuccess` | `/registration-success` | RegistrationSuccessScreen | Registration flow |
| **Main Routes** |
| `home` | `/home` | HomeScreen | Main dashboard |
| **Document Routes** |
| `captureDocument` | `/capture-document` | CaptureDocumentScreen | Document capture |
| `previewDocument` | `/preview-document` | PreviewDocumentScreen | Document preview |
| **Identity Routes** |
| `identityUpload` | `/identity-upload` | IdentityUploadPage | Identity verification |
| **Demo Routes** |
| `navigationDemo` | `/navigation-demo` | NavigationDemo | Testing navigation |

### 🎯 Navigation Patterns

#### 1. Push Navigation (có back button)
**Sử dụng khi:** User cần quay lại màn hình trước

```dart
// Registration Flow
context.goToSelectAccountType();    // Push
context.goToCtvPolicy();            // Push
context.goToKycIdGuide();           // Push
context.goToIdentityUpload();       // Push
context.goToPersonalInfoConfirmation(); // Push
context.goToRegistrationSuccess();  // Push

// Document Flow
context.goToCaptureDocument();      // Push
context.goToPreviewDocument();      // Push
```

#### 2. Replace Navigation (không có back button)
**Sử dụng khi:** User không nên quay lại màn hình trước

```dart
// Login Flow
context.replaceWithHome();         // Sau khi login thành công
context.replaceWithLogin();        // Khi logout

// Initial Navigation
context.goToLogin();               // App start
```

### 📱 Usage Examples

#### Basic Navigation
```dart
import 'package:sales_app/presentation/router/navigation_extensions.dart';

class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        // Type-safe navigation với auto-completion
        context.goToSelectAccountType();
      },
      child: Text('Go to Account Type'),
    );
  }
}
```

#### Navigation với Parameters
```dart
// Document preview với parameters
context.pushPreviewDocumentWithParams(
  image: capturedImage,
  side: DocumentSide.front,
  onAccept: (image) {
    // Handle accepted image
  },
);
```

### 🔧 Available Methods

#### Push Methods (có back button)
```dart
// Auth Flow
context.goToSelectAccountType();
context.goToCtvPolicy();
context.goToKycIdGuide();
context.goToIdentityUpload();
context.goToPersonalInfoConfirmation();
context.goToRegistrationSuccess();

// Document Flow
context.goToCaptureDocument();
context.goToPreviewDocument();
context.pushPreviewDocumentWithParams(...);

// General
context.pushToHome();               // Push home (not replace)
context.goToNavigationDemo();       // Demo screen
```

#### Replace Methods (không có back button)
```dart
// Login/Logout
context.goToLogin();                // Initial app start
context.goToHome();                 // After login (replace)
context.replaceWithLogin();         // For logout
context.replaceWithHome();          // For successful login

// General
context.replaceWith('/any-route');
context.clearAndGoTo('/any-route');
```

#### Utility Methods
```dart
// Navigation utilities
context.goBack();                   // Smart back với fallback
context.canPop();                   // Check if can go back
context.pop();                      // Go back (if possible)
```

### 🎯 Best Practices

#### ✅ DO
```dart
// Sử dụng type-safe methods
context.goToSelectAccountType();

// Đúng pattern cho login flow
void handleLogin() {
  context.replaceWithHome(); // Clear login from stack
}

// Đúng pattern cho registration flow
void handleRegistrationStep() {
  context.goToCtvPolicy(); // User có thể quay lại
}

// Import đúng
import 'package:sales_app/presentation/router/navigation_extensions.dart';
```

#### ❌ DON'T
```dart
// Không dùng Navigator.push trực tiếp
Navigator.push(context, MaterialPageRoute(...)); // ❌

// Không dùng sai pattern
void handleLogin() {
  context.pushToHome(); // ❌ User có thể quay lại login
}
```

## 📋 Logging System

### 🎯 Overview

Hệ thống logging được thiết kế theo kiến trúc Clean Architecture với khả năng tái sử dụng cao và dễ dàng bảo trì.

### 🏗️ Architecture

```
domain/services/logger_service.dart        // Interface
data/services_impl/logger_service_impl.dart // Implementation
core/utils/app_logger.dart                 // Helper class
```

### 📝 Log Levels

| Mức | Mô tả | Emoji | Màu |
|-----|-------|-------|-----|
| `info` | Thông tin chung | ℹ️ | Xanh lá |
| `debug` | Log khi phát triển | 🐛 | Xám |
| `warning` | Cảnh báo | ⚠️ | Vàng |
| `error` | Lỗi nghiêm trọng | ❌ | Đỏ |
| `event` | Sự kiện người dùng | 🎯 | Xanh lá |
| `navigation` | Navigation events | 🧭 | Xanh dương |

### 🚀 Usage

#### 1. AppLogger (Recommended)
```dart
import 'package:sales_app/core/utils/app_logger.dart';

// Log thông tin
AppLogger.info('Ứng dụng khởi tạo thành công');

// Log với data
AppLogger.info('User đăng nhập', data: {
  'userId': user.id,
  'email': user.email,
  'timestamp': DateTime.now().toIso8601String(),
});

// Log debug
AppLogger.debug('State changed', data: {'newState': state.toString()});

// Log cảnh báo
AppLogger.warning('Kết nối mạng không ổn định');

// Log lỗi
try {
  // Some code
} catch (e, stackTrace) {
  AppLogger.error('Đăng nhập thất bại',
    error: e,
    stackTrace: stackTrace,
    data: {'userId': userId}
  );
}

// Log sự kiện
AppLogger.event('Button clicked', data: {'buttonName': 'login'});

// Log navigation (tự động trong navigation extensions)
AppLogger.event('Navigation: Pushing to Select Account Type');
AppLogger.event('Navigation: Replacing with Home');
```

#### 2. Network Logging
```dart
// Log request
AppLogger.networkRequest('POST', '/api/login',
  headers: {'Content-Type': 'application/json'},
  body: {'email': email, 'password': '***'}
);

// Log response
AppLogger.networkResponse('POST', '/api/login', 200,
  response: responseData,
  duration: Duration(milliseconds: 500)
);
```

### 🎨 Log Format

```
🎯 [2024-01-15T10:30:45.123456] [INFO]
🎯 EVENT: User attempted login
Data: {email: true, timestamp: 2024-01-15T10:30:45.123456}
────────────────────────────────────────────────────────────────────────────────
```

### ⚙️ Configuration

#### Debug Environment
- Hiển thị tất cả log
- Có màu sắc và emoji
- Hiển thị timestamp
- Hiển thị stack trace cho error

#### Production Environment
- Không hiển thị log nào
- Tối ưu performance

### 📋 Best Practices

- ✅ Sử dụng `AppLogger` cho code ngắn gọn
- ✅ Log sự kiện quan trọng (user action, API call)
- ✅ Không log thông tin nhạy cảm (password, token)
- ✅ Sử dụng data parameter để cung cấp context
- ✅ Log error với stack trace
- ❌ Không log quá nhiều trong loop
- ❌ Không hardcode log message

## 🔄 Loading System

### 📋 Overview

Adaptive loading system với platform-specific indicators được thiết kế để:

- ✅ **Silent by default**: No messages unless explicitly provided
- ✅ **Adaptive Design**: iOS (CupertinoActivityIndicator) / Android (CircularProgressIndicator)
- ✅ **Reusable Components**: Use across all screens with consistent UX
- ✅ **Multiple Usage Patterns**: BaseWidget, Mixin, Extension, Direct usage
- ✅ **Type-safe**: Compile-time checking with enums

### 🎯 Key Features

- **Silent by Default**: Clean, minimal loading experience
- **Explicit Messaging**: Use `getLoadingMessage()` when needed
- **Adaptive**: iOS/Android platform-specific indicators
- **Reusable**: Consistent across all screens
- **Flexible**: Multiple usage patterns
- **Type-safe**: Compile-time checking
- **Professional**: Modern UX patterns

### 📱 Usage Patterns

#### 1. BaseLoadingHookWidget (Recommended)

**Silent Loading (Default):**
```dart
class DataScreen extends BaseLoadingHookWidget {
  @override
  bool isLoading(WidgetRef ref) {
    return ref.watch(dataProvider).maybeWhen(
      null, // $default parameter
      loading: () => true,
      orElse: () => false,
    );
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.loading;

  // No getLoadingMessage() = silent loading (no text)

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('Data')),
      body: YourContent(),
    );
  }
}
```

**Loading with Message:**
```dart
class LoginScreen extends BaseLoadingHookWidget {
  @override
  bool isLoading(WidgetRef ref) {
    return ref.watch(authProvider).maybeWhen(
      null,
      loading: () => true,
      orElse: () => false,
    );
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.login;

  @override
  String? getLoadingMessage(WidgetRef ref) => 'Đang đăng nhập...'; // ✅ Show message

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: LoginForm(),
    );
  }
}
```

#### 2. Extension Method (Simple Cases)
```dart
class SimpleScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = ref.watch(someProvider).isLoading;

    return Scaffold(
      body: YourContent(),
    ).withLoadingOverlay(
      isLoading: isLoading,
      message: 'Processing...', // Optional message
    );
  }
}
```

#### 3. LoadingMixin (Custom Control)
```dart
class CustomScreen extends ConsumerWidget with LoadingMixin {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = ref.watch(someProvider).isLoading;

    return Scaffold(
      body: Stack(
        children: [
          YourMainContent(),
          buildConfiguredLoadingOverlay(
            isLoading: isLoading,
            type: LoadingType.saving,
            customMessage: 'Custom message...', // Optional
          ),
        ],
      ),
    );
  }
}
```

### 🎨 Customization Options

#### AdaptiveLoadingOverlay Parameters
```dart
AdaptiveLoadingOverlay(
  message: 'Custom message',           // Loading text
  backgroundColor: Colors.black,       // Overlay background
  opacity: 0.5,                       // Background opacity (0.0 - 1.0)
  showMessage: true,                   // Show/hide message
  messageStyle: TextStyle(...),       // Custom text style
)
```

#### LoadingOverlayConfig
```dart
// Silent loading (DEFAULT)
LoadingOverlayConfig.silent()

// Custom message
LoadingOverlayConfig.withMessage('Custom message...')

// Legacy methods with predefined messages
LoadingOverlayConfig.loginWithMessage()     // "Đang đăng nhập..."
LoadingOverlayConfig.logoutWithMessage()    // "Đang đăng xuất..."
LoadingOverlayConfig.savingWithMessage()    // "Đang lưu..."
LoadingOverlayConfig.deletingWithMessage()  // "Đang xóa..."
LoadingOverlayConfig.loadingWithMessage()   // "Đang tải..."
```

### 📊 Platform-Specific Indicators

#### iOS
```dart
// Automatically uses CupertinoActivityIndicator
if (Platform.isIOS) {
  return const CupertinoActivityIndicator(radius: 16);
}
```

#### Android
```dart
// Automatically uses CircularProgressIndicator
return CircularProgressIndicator(
  valueColor: AlwaysStoppedAnimation<Color>(
    Theme.of(context).colorScheme.primary,
  ),
);
```

### 📊 Recommended Usage

| Screen Type | Recommendation | Example |
|-------------|----------------|---------|
| **Data Loading** | ✅ Silent | List refresh, pagination |
| **Quick Actions** | ✅ Silent | Like, favorite, bookmark |
| **Important Operations** | 📝 With Message | Login, payment, upload |
| **Long Operations** | 📝 With Message | Sync, backup, export |
| **Background Tasks** | ✅ Silent | Auto-save, cache update |

## 🔌 Mock API System

### 📋 Overview

Modular mock interceptors provide mock API functionality organized by domain. Each interceptor handles specific API endpoints, making the system scalable and maintainable.

**Current Status**: ✅ Ready to use with Auth APIs mock implementation.

### 🚀 Quick Start

#### 1. Test Login
```dart
// App is already in Mock Mode (AppConstants.isMockMode = true)
await authController.login(
  email: '<EMAIL>',
  password: '123456',
);
```

#### 2. Available Mock Users
| Email | Password | Name | Role |
|-------|----------|------|------|
| <EMAIL> | 123456 | Admin User | admin |
| <EMAIL> | password | Test User | user |
| <EMAIL> | demo123 | Demo User | demo |

#### 3. Switch to Real API
```dart
// In lib/core/constants/app_constants.dart
static const bool isMockMode = false;
```

### 🏗️ Architecture

#### Base Class
- `base_mock_interceptor.dart` - Abstract base class with common functionality
- Provides network simulation, response creation, error handling

#### Domain-Specific Interceptors
- `mock_auth_interceptor.dart` - Authentication APIs (uses `ApiPaths` constants)

#### API Path Constants
- `lib/core/constants/api_paths.dart` - Centralized API endpoint definitions
- Prevents hardcoding paths in interceptors and services

### 🔄 How It Works

#### Request Flow:
1. **Real API Mode**: Request passes through all interceptors → Real API
2. **Mock Mode**: Appropriate interceptor catches request → Returns mock data

#### Interceptor Order:
```
LoggingInterceptor
↓
MockAuthInterceptor     ← Catches /auth/* requests in mock mode
↓
AuthInterceptor         ← Always adds auth headers when token available
↓                         (excluded for AuthApiService)
ErrorInterceptor        ← Handles errors
↓
Real API Call (if not mocked)
```

#### Flexible Configuration:
- **Default APIs**: All interceptors (logging, mock, auth, error)
- **Auth APIs**: Core + mock interceptors (no auth headers)
- **Custom APIs**: Configurable interceptor combinations

### 🎯 Features

#### Mock Auth Interceptor
- ✅ **Login**: `ApiPaths.authLogin` - Validates credentials against mock users
- ✅ **Logout**: `ApiPaths.authLogout` - Always succeeds
- ✅ **Register**: `ApiPaths.authRegister` - Mock registration
- ✅ **Refresh Token**: `ApiPaths.authRefresh` - Mock token refresh

### ⚙️ Configuration

#### Enable/Disable Mock Mode
```dart
// Enable mock mode
AppConstants.isMockMode = true;

// Disable mock mode (use real API)
AppConstants.isMockMode = false;
```

#### Network Simulation
Each interceptor simulates:
- **Realistic delays**: 500ms - 2s random delay
- **Error simulation**: 10% chance of network error (optional)
- **Proper HTTP status codes**: 200, 401, 404, 500, etc.
- **API Paths**: Uses `ApiPaths` constants instead of hardcoding

### 🔧 Adding New Mock Interceptors

When you need to add more mock APIs (e.g., for users, products, orders):

#### Step 1: Create New Interceptor
```dart
// lib/data/network/interceptors/mock/mock_user_interceptor.dart
@injectable
class MockUserInterceptor extends BaseMockInterceptor {
  @override
  bool shouldHandle(RequestOptions options) {
    return ApiPaths.isUserPath(options.path);
  }

  @override
  void handleMockRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    await simulateNetworkDelay();

    if (options.path.contains(ApiPaths.userProfile) && options.method == 'GET') {
      _handleGetProfile(options, handler);
    }
    // ... other handlers
  }

  void _handleGetProfile(RequestOptions options, RequestInterceptorHandler handler) {
    final profileData = {
      'id': '1',
      'email': '<EMAIL>',
      'name': 'Current User',
    };
    handler.resolve(createSuccessResponse(options, profileData));
  }
}
```

#### Step 2: Register in ApiClient
```dart
// lib/data/network/api_client.dart
class ApiClient {
  final MockUserInterceptor _mockUserInterceptor;

  ApiClient(
    // ... existing interceptors
    this._mockUserInterceptor,
  );

  List<Interceptor> _getDefaultInterceptors() {
    return [
      _loggingInterceptor,
      _mockAuthInterceptor,
      _mockUserInterceptor,  // Add here
      _authInterceptor,
      _errorInterceptor,
    ];
  }
}
```

#### Step 3: Regenerate DI
```bash
dart run build_runner build --delete-conflicting-outputs
```

### ✅ Benefits

#### Advantages
- **Scalable**: Easy to add new domain interceptors
- **Maintainable**: Each domain has its own file
- **Team-friendly**: Multiple developers can work on different interceptors
- **Clean**: No mixing of mock/real logic in API services
- **Flexible**: Can mock some APIs while using real ones for others

### 🧪 Testing

#### Manual Testing
1. Set mock mode: `AppConstants.isMockMode = true`
2. Use app normally
3. Check console for "Mock Interceptor" logs
4. Verify mock data is returned

#### Unit Testing
```dart
test('Mock auth interceptor handles login', () async {
  final interceptor = MockAuthInterceptor();
  final options = RequestOptions(path: '/auth/login', method: 'POST');
  options.data = {'email': '<EMAIL>', 'password': '123456'};

  // Test interceptor logic
  expect(interceptor.shouldHandle(options), true);
});
```

### 🔄 Migration Path

When real APIs become available:

1. **Keep interceptors**: Useful for testing and development
2. **Switch mode**: Change to `AppConstants.isMockMode = false`
3. **Gradual migration**: Can mock some APIs while using real ones for others
4. **Remove when ready**: Delete interceptors when no longer needed

### 📋 Best Practices

1. **Keep mock data realistic** - Use proper data structures
2. **Simulate errors** - Test error handling paths
3. **Log everything** - Use AppLogger for debugging
4. **Follow REST conventions** - Proper HTTP status codes
5. **Document endpoints** - Update documentation when adding new mocks

---

**This guide covers all the essential systems in the Sales App. Each system is designed to work together seamlessly while maintaining clean architecture principles.**
